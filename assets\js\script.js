document.addEventListener("DOMContentLoaded", function () {
  const sliderContainer = document.querySelector(".slider-container");
  const slides = document.querySelectorAll(".slide");
  let currentIndex = 0;

  // Initialize first slide
  slides[currentIndex].classList.add("active");

  // Set up interval for slide transition
  setInterval(() => {
    // Fade out current slide
    slides[currentIndex].classList.remove("active");

    // Calculate next slide index
    currentIndex = (currentIndex + 1) % slides.length;

    // Fade in next slide
    slides[currentIndex].classList.add("active");
  }, 7000); // Change slide every 5 seconds
});

// document.addEventListener("DOMContentLoaded", function () {
//   const sliderRow = document.querySelector(".services-slider");
//   const slides = document.querySelectorAll(".service-slide");
//   let interval = null;
//   let currentTranslate = 0;
//   let direction = -1; // -1 for forward, 1 for backward
//   const slideStep = 50;
//   const maxTranslate = -slideStep * (slides.length - 2);
//   const mobileMedia = window.matchMedia("(max-width: 767px)");

//   // Touch variables
//   let startX = 0;
//   let isDragging = false;
//   let startTranslate = 0;
//   let currentX = 0;

//   function autoplaySlide() {
//     currentTranslate += direction * slideStep;

//     if (currentTranslate < maxTranslate || currentTranslate > 0) {
//       // Reverse direction
//       direction *= -1;
//       currentTranslate += direction * slideStep; // Step back in new direction
//     }

//     sliderRow.style.transition = "transform 0.5s ease-in-out";
//     sliderRow.style.transform = `translateX(${currentTranslate}%)`;
//   }

//   function startAutoplay() {
//     clearInterval(interval);
//     interval = setInterval(autoplaySlide, 3000);
//   }

//   function handleMobileChange(e) {
//     if (e.matches) {
//       sliderRow.style.transform = "translateX(0%)";
//       currentTranslate = 0;
//       direction = -1;
//       startAutoplay();
//       setupTouchEvents();
//     } else {
//       clearInterval(interval);
//       interval = null;
//       sliderRow.style.transform = "translateX(0%)";
//       removeTouchEvents();
//     }
//   }

//   function setupTouchEvents() {
//     sliderRow.addEventListener("touchstart", handleTouchStart);
//     sliderRow.addEventListener("touchmove", handleTouchMove);
//     sliderRow.addEventListener("touchend", handleTouchEnd);
//   }

//   function removeTouchEvents() {
//     sliderRow.removeEventListener("touchstart", handleTouchStart);
//     sliderRow.removeEventListener("touchmove", handleTouchMove);
//     sliderRow.removeEventListener("touchend", handleTouchEnd);
//   }

//   function handleTouchStart(e) {
//     if (!mobileMedia.matches) return;
//     startX = e.touches[0].clientX;
//     isDragging = true;
//     startTranslate = currentTranslate;
//     clearInterval(interval);
//     sliderRow.style.transition = "none";
//   }

//   function handleTouchMove(e) {
//     if (!isDragging || !mobileMedia.matches) return;
//     currentX = e.touches[0].clientX;
//     const diffX = currentX - startX;
//     const percentMove = (diffX / window.innerWidth) * 100;
//     const newTranslate = startTranslate + percentMove;

//     currentTranslate = Math.max(Math.min(newTranslate, 0), maxTranslate);
//     sliderRow.style.transform = `translateX(${currentTranslate}%)`;
//     e.preventDefault();
//   }

//   function handleTouchEnd() {
//     if (!isDragging || !mobileMedia.matches) return;
//     isDragging = false;
//     sliderRow.style.transition = "transform 0.3s ease-in-out";

//     const diffX = currentX - startX;
//     const threshold = window.innerWidth * 0.1;

//     if (Math.abs(diffX) > threshold) {
//       if (diffX > 0) {
//         currentTranslate = Math.min(currentTranslate + slideStep, 0);
//         direction = 1;
//       } else {
//         currentTranslate = Math.max(currentTranslate - slideStep, maxTranslate);
//         direction = -1;
//       }
//     } else {
//       currentTranslate = Math.round(currentTranslate / slideStep) * slideStep;
//     }

//     sliderRow.style.transform = `translateX(${currentTranslate}%)`;
//     setTimeout(startAutoplay, 1000);
//   }

//   // Init
//   handleMobileChange(mobileMedia);

//   if (mobileMedia.addEventListener) {
//     mobileMedia.addEventListener("change", handleMobileChange);
//   } else if (mobileMedia.addListener) {
//     mobileMedia.addListener(handleMobileChange);
//   }

//   sliderRow.addEventListener("mouseenter", () => {
//     if (mobileMedia.matches) clearInterval(interval);
//   });

//   sliderRow.addEventListener("mouseleave", () => {
//     if (mobileMedia.matches && !interval) startAutoplay();
//   });
// });

document.addEventListener("DOMContentLoaded", function () {
  const container = document.querySelector(".products-slider-container");
  const slider = document.querySelector(".products-slider");
  const slides = document.querySelectorAll(".product-slide");
  let intervalId = null;
  let currentTranslate = 0;
  let slidesPerView = 6;
  let autoplayDirection = "next"; // Track direction for ping-pong effect

  // Swipe variables
  let isDragging = false;
  let startPos = 0;
  let currentPos = 0;
  let currentTranslateStart = 0;
  let animationID = null;
  let swipeThreshold = 50;

  function getSlidesPerView() {
    const width = window.innerWidth;
    if (width >= 1024) return 6;
    if (width >= 768) return 4;
    return 3;
  }

  function setSliderDimensions() {
    const containerWidth = container.offsetWidth;
    slidesPerView = getSlidesPerView();
    const slideWidth = containerWidth / slidesPerView;

    slides.forEach((slide) => {
      slide.style.width = `${slideWidth}px`;
    });

    slider.style.width = `${slideWidth * slides.length}px`;
  }

  function moveSlider(direction = autoplayDirection) {
    const containerWidth = container.offsetWidth;
    const slideWidth = containerWidth / slidesPerView;
    const maxTranslate = -slideWidth * (slides.length - slidesPerView);

    if (direction === "next") {
      currentTranslate -= slideWidth;

      if (currentTranslate <= maxTranslate) {
        currentTranslate = maxTranslate;
        autoplayDirection = "prev"; // reverse direction at end
      }
    } else {
      currentTranslate += slideWidth;

      if (currentTranslate >= 0) {
        currentTranslate = 0;
        autoplayDirection = "next"; // reverse direction at start
      }
    }

    slider.style.transition = "transform 0.5s ease-in-out";
    slider.style.transform = `translateX(${currentTranslate}px)`;
  }

  function startAutoplay() {
    if (!intervalId) {
      intervalId = setInterval(() => moveSlider(autoplayDirection), 3000);
    }
  }

  function stopAutoplay() {
    clearInterval(intervalId);
    intervalId = null;
  }

  function handleResize() {
    setSliderDimensions();
    currentTranslate = 0;
    autoplayDirection = "next"; // Reset to forward direction
    slider.style.transform = "translateX(0px)";
    stopAutoplay();
    startAutoplay();
  }

  // Swipe functionality
  function touchStart(event) {
    stopAutoplay();
    isDragging = true;
    startPos = getPositionX(event);
    currentTranslateStart = currentTranslate;

    cancelAnimationFrame(animationID);

    slider.style.cursor = "grabbing";
    slider.style.transition = "none";
  }

  function touchMove(event) {
    if (!isDragging) return;

    currentPos = getPositionX(event);
    const diff = currentPos - startPos;

    slider.style.transform = `translateX(${currentTranslateStart + diff}px)`;

    event.preventDefault();
  }

  function touchEnd() {
    if (!isDragging) return;

    isDragging = false;
    const movedBy = currentPos - startPos;

    slider.style.cursor = "grab";
    slider.style.transition = "transform 0.5s ease-in-out";

    if (Math.abs(movedBy) > swipeThreshold) {
      if (movedBy < 0) {
        moveSlider("next");
      } else {
        moveSlider("prev");
      }
    } else {
      slider.style.transform = `translateX(${currentTranslate}px)`;
    }

    startAutoplay();
  }

  function getPositionX(event) {
    return event.type.includes("mouse")
      ? event.pageX
      : event.touches[0].clientX;
  }

  // Initialize
  setSliderDimensions();
  startAutoplay();

  // Event listeners
  window.addEventListener("resize", handleResize);
  slider.addEventListener("mouseenter", stopAutoplay);
  slider.addEventListener("mouseleave", () => {
    if (!isDragging) startAutoplay();
  });

  slider.addEventListener("touchstart", touchStart);
  slider.addEventListener("touchmove", touchMove);
  slider.addEventListener("touchend", touchEnd);

  slider.addEventListener("mousedown", touchStart);
  slider.addEventListener("mousemove", touchMove);
  slider.addEventListener("mouseup", touchEnd);
  slider.addEventListener("mouseleave", touchEnd);

  slider.addEventListener("contextmenu", (e) => e.preventDefault());
});

document.addEventListener("DOMContentLoaded", function () {
  // Get the mobile menu and toggle button
  const mobileMenu = document.querySelector(".mobile-menu");
  const menuButton = document.querySelector(".header-mobile-button");

  // Initially hide the mobile menu off-screen
  mobileMenu.style.transform = "translateX(-100%)";
  mobileMenu.style.transition = "transform 0.3s ease-in-out";

  // Toggle menu when button is clicked
  menuButton.addEventListener("click", function (event) {
    event.stopPropagation(); // Prevent this click from triggering the document click

    // If menu is hidden, show it
    if (mobileMenu.style.transform === "translateX(-100%)") {
      mobileMenu.style.transform = "translateX(0)";
    } else {
      // Otherwise hide it
      mobileMenu.style.transform = "translateX(-100%)";
    }
  });

  // Close menu when clicking outside
  document.addEventListener("click", function (event) {
    // Check if the click is outside the mobile menu
    if (!mobileMenu.contains(event.target) && event.target !== menuButton) {
      mobileMenu.style.transform = "translateX(-100%)";
    }
  });

  // Prevent clicks inside the menu from closing it
  mobileMenu.addEventListener("click", function (event) {
    event.stopPropagation();
  });
});

document.addEventListener("DOMContentLoaded", function () {
  // Get all FAQ triggers
  const faqTriggers = document.querySelectorAll(".faq-trigger");

  // Initialize all content sections to height 0
  document.querySelectorAll(".faq-content").forEach((content) => {
    content.style.height = "0px";
  });

  // Add click event listener to each trigger
  faqTriggers.forEach((trigger) => {
    trigger.addEventListener("click", function () {
      // Get the parent faq-box
      const faqBox = this.parentElement;

      // Get the content element that follows the trigger
      const content = faqBox.querySelector(".faq-content");

      // Check if this accordion is already active
      const isActive = this.classList.contains("active");

      // Close all accordions first
      closeAllAccordions();

      // If the clicked accordion wasn't active, open it
      if (!isActive) {
        this.classList.add("active");
        content.classList.add("active");

        // Get the height of the inner content with error handling
        try {
          const contentInner = content.querySelector(".faq-content-inner");
          if (contentInner) {
            // Force a reflow to ensure accurate height calculation
            void contentInner.offsetHeight;
            const contentHeight = contentInner.offsetHeight;
            content.style.height = contentHeight + "px";
          }
        } catch (error) {
          console.error("Error calculating content height:", error);
        }
      }
    });
  });

  // Function to close all accordions
  function closeAllAccordions() {
    // Remove active class from all triggers and content
    document
      .querySelectorAll(".faq-trigger, .faq-content")
      .forEach((element) => {
        element.classList.remove("active");
      });

    // Set height to 0 for all content sections
    document.querySelectorAll(".faq-content").forEach((content) => {
      content.style.height = "0px";
    });
  }

  // Add window resize handler to recalculate heights of open accordions
  window.addEventListener("resize", function () {
    const activeContent = document.querySelector(".faq-content.active");
    if (activeContent) {
      const contentInner = activeContent.querySelector(".faq-content-inner");
      if (contentInner) {
        activeContent.style.height = contentInner.offsetHeight + "px";
      }
    }
  });
});

// ===== IMAGE OPTIMIZATION FEATURES =====

// Lazy Loading Implementation
document.addEventListener("DOMContentLoaded", function () {
  // Check if browser supports Intersection Observer
  if ("IntersectionObserver" in window) {
    // Create intersection observer for lazy loading
    const imageObserver = new IntersectionObserver(
      (entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target;

            // Load the image
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute("data-src");
            }

            // Handle srcset for responsive images
            if (img.dataset.srcset) {
              img.srcset = img.dataset.srcset;
              img.removeAttribute("data-srcset");
            }

            // Add loaded class for styling
            img.classList.add("lazy-loaded");
            img.classList.remove("lazy-loading");

            // Stop observing this image
            observer.unobserve(img);
          }
        });
      },
      {
        // Load images when they're 50px away from viewport
        rootMargin: "50px 0px",
        threshold: 0.01,
      }
    );

    // Observe all images with lazy loading
    document.querySelectorAll("img[data-src]").forEach((img) => {
      img.classList.add("lazy-loading");
      imageObserver.observe(img);
    });
  } else {
    // Fallback for browsers without Intersection Observer
    document.querySelectorAll("img[data-src]").forEach((img) => {
      img.src = img.dataset.src;
      img.removeAttribute("data-src");
      if (img.dataset.srcset) {
        img.srcset = img.dataset.srcset;
        img.removeAttribute("data-srcset");
      }
    });
  }
});

// Image Error Handling and Fallbacks
document.addEventListener("DOMContentLoaded", function () {
  // Handle image loading errors
  document.addEventListener(
    "error",
    function (e) {
      if (e.target.tagName === "IMG") {
        const img = e.target;

        // Try WebP fallback first if available
        if (
          img.dataset.fallback &&
          !img.classList.contains("fallback-attempted")
        ) {
          img.classList.add("fallback-attempted");
          img.src = img.dataset.fallback;
          return;
        }

        // Add error class for styling
        img.classList.add("image-error");

        // Optional: Replace with placeholder
        if (img.dataset.placeholder) {
          img.src = img.dataset.placeholder;
        }
      }
    },
    true
  );
});

// Performance Monitoring
document.addEventListener("DOMContentLoaded", function () {
  // Monitor image loading performance
  if ("PerformanceObserver" in window) {
    const perfObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.initiatorType === "img") {
          // Log slow loading images (>2 seconds)
          if (entry.duration > 2000) {
            console.warn(
              `Slow loading image detected: ${entry.name} (${entry.duration}ms)`
            );
          }
        }
      });
    });

    perfObserver.observe({ entryTypes: ["resource"] });
  }
});
