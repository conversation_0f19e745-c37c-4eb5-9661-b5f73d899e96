html,
body,
div,
span,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
abbr,
address,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
samp,
small,
strong,
sub,
sup,
var,
b,
i,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
}

body {
  line-height: 1;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

a {
  margin: 0;
  padding: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
  text-decoration: none;
}

/* change colours to suit your needs */
ins {
  background-color: #ff9;
  color: #000;
  text-decoration: none;
}

/* change colours to suit your needs */
mark {
  background-color: #ff9;
  color: #000;
  font-style: italic;
  font-weight: bold;
}

del {
  text-decoration: line-through;
}

abbr[title],
dfn[title] {
  border-bottom: 1px dotted;
  cursor: help;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* change border colour to suit your needs */
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #cccccc;
  margin: 1em 0;
  padding: 0;
}

input,
select {
  vertical-align: middle;
}

.montserrat {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
}

body {
  font-family: "Montserrat", sans-serif;
}

.header-desktop {
  background-color: #1d1e6a;
}

.main-nav-item {
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0px;
  text-transform: uppercase;
  font-family: "Overlock", sans-serif;
  font-weight: 700;
  font-style: normal;
  color: #fff;
}

.contact-us-btn {
  background-color: #ffc222;
  color: #280e0b;
  padding: 16px 38px;
  border-radius: 30px;
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0px;
  text-transform: uppercase;
  font-family: "Overlock", sans-serif;
  font-weight: 700;
  font-style: normal;
  display: inline-block;
}

.main-nav-phone {
  font-weight: 700;
  font-size: 20px;
  letter-spacing: 0px;
  text-transform: uppercase;
  font-family: "Overlock", sans-serif;
  font-weight: 700;
  font-style: normal;
  color: #fff;
  display: inline-block;
}

.main-nav-phone span {
  font-size: 24px;
  color: #ffc222;
  margin-top: 4px;
  display: inline-block;
  font-weight: 900;
}

.banner-logo {
  width: 186px;
  height: 186px;
}

.hero-section {
  overflow: hidden;
  background-color: #111;
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 1;
  align-items: center;
  z-index: 1;
  height: 1085px;
}
.hero-section::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(4, 0, 0, 0.696);
  pointer-events: none;
  z-index: -1;
}
.pizza-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}
.banner-title,
.banner-subtitle {
  color: #fff;
  font-style: italic;
  text-transform: uppercase;
  transform: rotate(-5deg);
}

.banner-title {
  font-weight: 700;
  font-size: 67px;
  line-height: 100%;
  font-family: "Montserrat", sans-serif;
}

.banner-subtitle {
  font-weight: 800;
  font-size: 96px;
  line-height: 100%;
  font-family: "Montserrat", sans-serif;
}

.slider-container {
  position: relative;
  width: 100%;
  /* overflow: hidden; */
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  visibility: hidden;
  pointer-events: none;
}

.slide .banner-order-now {
  display: none !important;
}

.slide.active .banner-order-now {
  display: flex !important;
}

.slide.active {
  opacity: 1;
  visibility: visible;
  pointer-events: auto; /* Only active slide is clickable */
}

.services-section {
  padding-top: 80px;
}

.services-box {
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  height: 331px;
  box-shadow: 0px 3px 31.32px 0px #2828281a;
  border: 1.04px solid #dcdcdc;
  background-color: #fff;
  font-family: "Montserrat", sans-serif;
}

.services-box p {
  font-weight: 600;
  font-size: 24px;
  text-transform: capitalize;
  color: #280e0b;
  margin: 28px 0 8px 0;
}

.services-box h3 {
  font-weight: 700;
  font-size: 32px;
  text-align: center;
  text-transform: uppercase;
  color: #280e0b;
}

/* .services-box img {
  width: 110px;
  height: 110px;
} */

.services-title {
  font-weight: 700;
  font-size: 40px;
  letter-spacing: 1px;
  text-transform: capitalize;
  font-family: "Montserrat", sans-serif;
}

.services-subtitle {
  font-weight: 600;
  font-size: 16px;
  text-transform: capitalize;
  font-family: "Montserrat", sans-serif;
  letter-spacing: 1px;
}

.pr-text {
  padding-right: 100px;
}

/* .services-slider {
  display: flex;
  flex-wrap: nowrap;
  transition: transform 0.5s ease-in-out;
} */

/* .service-slide {
  flex: 0 0 25%;
  max-width: 25%;
} */

/* .product-card .img-box {
  background-color: #f7f7f7;
  padding: 60px 0;
  border-radius: 10px;
}

.product-card .img-box img {
  width: 262px;
  height: 178px;
} */

.product-card h3 {
  font-weight: 700;
  font-size: 24px;
}

.product-card a {
  font-weight: 400;
  font-size: 20px;
  text-decoration: underline;
  color: #38343a;
}

.products-slider-container {
  overflow: hidden;
  width: 100%;
}

.products-slider {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.product-slide {
  flex-shrink: 0;
  padding: 0 12px;
  box-sizing: border-box;
}

.product-card-2 {
  border-radius: 9px;
  z-index: 1;
  overflow: hidden;
  position: relative;
  height: 346px;
}

.product-card-2.card-1 img {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: -1;
}

.product-card-2 .content {
  padding: 40px 20px 0;
}

.product-card-2.card-1 p {
  padding-right: 83px;
}

/* .product-card-2.card-2 h4 {
  font-size: 33px;
} */

/* .product-card-2.card-1 img {
  top: -78px;
}
.product-card-2.card-2 img {
  top: 0;
}

.product-card-2 img {
  position: relative;
} */

.product-card-2.card-1 {
  background: radial-gradient(50% 50% at 50% 50%, #ffc222 0%, #f5b306 100%);
}
.product-card-2.card-2 {
  background: radial-gradient(50% 50% at 50% 50%, #10a757 0%, #00833e 100%);
}
.product-card-2.card-2 h3 {
  color: #ffc222;
}
.product-card-2.card-2 h4,
.product-card-2.card-2 p {
  color: #fff;
}
.product-card-2.card-1 h3 {
  color: #c10a28;
}
.product-card-2.card-2.bg-change {
  background: radial-gradient(50% 50% at 50% 50%, #ff7e45 0%, #d54200 100%);
}
.product-card-2.card-1 h4,
.product-card-2.card-1 p {
  color: #38343a;
}

.product-card-2 h3 {
  font-weight: 500;
  font-size: 24px;
  text-transform: capitalize;
  font-family: "Oswald", sans-serif;
}

/* .product-card-2 h4 {
  font-weight: 700;
  font-size: 40px;
  text-transform: capitalize;
  font-family: "Oswald", sans-serif;
} */

.product-card-2 p {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  text-transform: capitalize;
  margin: 10px 0;
  font-family: "Montserrat", sans-serif;
}

.product-card-2.card-2.bg-change h4,
.product-card-2 h4 {
  font-size: 32px;
  font-weight: 700;
  font-family: "Oswald", sans-serif;
}

.product-card-2.card-2 img {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: -1;
}

.envelope-img {
  /* width: 150px; */
  position: absolute;
  transform: translate(-50%, -50%);
  animation: bounce 2s infinite;
  z-index: 1;
  top: 10%;
  left: 14%;
}

.envelope-container {
  position: relative;
  width: 400px;
  height: 346px;
}

.envelope {
  width: 100%;
  height: 100%;
  border-top-left-radius: 9px;
  border-bottom-left-radius: 9px;
}

.newsletter {
  border: 1px solid #dcdcdc;
  background-color: #f7f7f7;
}

.newsletter-input {
  border: 1px solid #dcdcdc;
  background: #ffffff;
  height: 48px;
  border-radius: 5px;
}

.btn-form {
  background-color: #d32512;
  border: 1px solid #d32512;
  color: #fff;
  font-weight: 500;
  letter-spacing: 1px;
  text-align: center;
  width: 100%;
  padding: 14px 0;
  border-radius: 5px;
}

.form-title {
  font-weight: 700;
  font-size: 32px;
  letter-spacing: 1px;
  text-transform: capitalize;
  color: #38343a;
}

.form-desc {
  color: #38343a;
  font-weight: 500;
}

.newsletter {
  padding: 40px;
  width: 100%;
  border-top-right-radius: 9px;
  border-bottom-right-radius: 9px;
}

.categories-left-box {
  width: 863px;
  height: 473px;
  border-top-right-radius: 89px;
  border-bottom-right-radius: 89px;
}

.bg-green {
  background-color: #00833e;
}

.text-wrapper-categories h3 {
  font-weight: 500;
  font-size: 39px;
  line-height: 33.8px;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: #c10a28;
  font-family: "Oswald", sans-serif;
  margin-bottom: 6px;
}

.text-wrapper-categories h2 {
  font-weight: 900;
  font-size: 90px;
  line-height: 100%;
  color: #38343a;
  text-transform: uppercase;
}

.text-wrapper-categories p {
  font-weight: 500;
  font-size: 32px;
  line-height: 46px;
  letter-spacing: 1%;
  text-transform: capitalize;
  color: #c10a28;
  font-family: "Oswald", sans-serif;
}

.fav-apps-box h3 {
  font-weight: 700;
  font-size: 24px;
  line-height: 28px;
  text-transform: capitalize;
  color: #c10a28;
  font-family: "Oswald", sans-serif;
}

.fav-apps-box h4 {
  font-weight: 800;
  font-size: 22px;
  line-height: 28px;
  text-transform: capitalize;
}

.fav-apps-links {
  border-radius: 5px;
  background-color: #ffffff;
  /* width: 424px; */
  padding: 22px 30px;
  transition: all 0.3s ease-in-out;
}
.fav-apps-links:hover {
  background-color: #f6a525;
}
.fav-apps-links.box-2 {
  padding-top: 22px;
  padding-bottom: 22px;
}

.fav-apps-links img {
  width: 58px;
  height: 66px;
}
.fav-apps-links img.third {
  width: 101px;
  height: 20px;
}
.fav-apps-links img.fourth {
  width: 90px;
  height: 46px;
}
.fav-apps-links img.fifth {
  width: 92px;
  height: 47px;
}

.fav-apps-links span {
  font-weight: 500;
  font-size: 17px;
  text-transform: capitalize;
  color: #212121;
  font-family: "Oswald", sans-serif;
}

.fav-apps-section {
  background-color: #f5f5f5;
  margin-top: 110px;
  padding-top: 80px;
  padding-bottom: 80px;
}

.map-section h3 {
  font-weight: 700;
  font-size: 26px;
  line-height: 31.2px;
  color: #3c3c3e;
}

.map-section p {
  font-weight: 500;
  font-size: 24px;
  line-height: 28.8px;
  vertical-align: middle;
  color: #838383;
}

.footer {
  background-color: #1d1e6a;
}

.footer-desc {
  font-weight: 500;
  font-size: 20px;
  line-height: 36px;
  vertical-align: middle;
  color: #ffffff;
}

.footer-desc2 {
  font-weight: 500;
  font-size: 16px;
  line-height: 25px;
  color: #ffffff;
}

.footer-title {
  font-weight: 700;
  font-size: 34px;
  letter-spacing: 1px;
  text-transform: capitalize;
  color: #ffc222;
}

.footer-input {
  border: 1px solid #d32512;
  height: 48px;
  border-radius: 5px;
  background-color: #fff;
  color: #757575;
  font-weight: 400;
  font-size: 14px;
}

textarea.footer-input {
  height: 133px;
}

.btn-form-footer {
  background-color: #d32512;
  border: 1px solid #d32512;
  color: #fff;
  font-weight: 500;
  letter-spacing: 1px;
  text-align: center;
  width: 100%;
  padding: 14px 0;
  border-radius: 5px;
}

.footer-input::placeholder {
  color: #757575;
  font-weight: 400;
  font-size: 14px;
}

.footer-top {
  border-bottom: 1px solid #ffffff1a;
  padding: 40px 0;
}

.footer-address,
.footer-email {
  font-weight: 600;
  font-size: 32px;
  line-height: 49px;
}

.footer-address {
  color: #ffffff;
}

.footer-email {
  color: #d4b727;
}

.footer-copyright {
  font-weight: 500;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 1px;
  color: #ffffff;
}

.header-mobile-button {
  position: absolute;
  top: 40px;
  left: 10px;
  z-index: 10;
  color: #fff;
}

.overlock-regular {
  font-family: "Overlock", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.overlock-black {
  font-family: "Overlock", sans-serif;
  font-weight: 900;
  font-style: normal;
}

.overlock-regular-italic {
  font-family: "Overlock", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.overlock-bold-italic {
  font-family: "Overlock", sans-serif;
  font-weight: 700;
  font-style: italic;
}

.overlock-black-italic {
  font-family: "Overlock", sans-serif;
  font-weight: 900;
  font-style: italic;
}

.mobile-nav-item {
  border-bottom: 1px solid #000000;
  padding: 24px 0 16px;
  margin: 0 24px;
}

.mobile-nav-item a {
  font-weight: 900;
  font-size: 20px;
  color: #1d1e6a;
}

.main-nav-phone-mobile .text-phone-mobile {
  color: #c10a28;
  font-weight: 900;
  font-size: 20px;
  display: block;
  text-transform: uppercase;
}

.main-nav-phone-mobile .phone-num-mobile {
  font-weight: 900;
  font-size: 24px;
  color: #1d1e6a;
  display: block;
}

.main-nav-register-mobile {
  background-color: #ffc222;
  font-weight: 500;
  font-size: 16px;
  text-align: center;
  text-transform: capitalize;
  width: 100%;
  padding: 14px 0;
  border-radius: 9px;
  color: #1d1e6a;
  display: block;
}

.mobile-menu-top {
  padding: 36px 0 120px;
}

.mobile-menu-bottom {
  margin: 0 24px;
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 80%;
  height: 100vh;
  background-color: #fff;
  z-index: 100;
  overflow-y: auto;
}

.banner-order-now {
  width: 160px;
  height: 160px;
  background-color: #ffc222;
  font-weight: 400;
  font-size: 32px;
  letter-spacing: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #280e0b;
  border-radius: 50%;
  position: absolute;
  right: 150px;
  top: 56px;
  font-family: "Oswald", sans-serif;
  text-transform: uppercase;
  z-index: 2;
}

/* .banner-img-1 {
  width: 1044px;
  height: 506px;
  object-fit: cover;
}
.banner-img-2 {
  object-fit: cover;
  width: 630px;
} */
/* .banner-img-3 {
  object-fit: cover;
  width: 609px;
  height: 606px;
} */

.customer-fav-section {
  padding-top: 100px;
}

.services-box:hover {
  background: radial-gradient(50% 50% at 50% 50%, #353692 0%, #1d206a 100%);
  border: 1.04px solid #dcdcdc;
  box-shadow: 0px 3px 31.32px 0px #2828281a;
  transition: all 0.5s ease-in-out;
}

.services-box:hover h3,
.services-box:hover p {
  color: #fff;
}

.middle-banner-section {
  padding-top: 120px;
}

.categories-section {
  padding-top: 120px;
}

.reward-box {
  background: radial-gradient(50% 50% at 50% 50%, #353692 0%, #1d1e6a 100%);
}

.reward-box h2 {
  color: #fff;
  font-weight: 900;
  text-transform: uppercase;
}

.reward-box h3 {
  font-weight: 800;
  text-transform: capitalize;
  color: #fff;
}

.reward-box p {
  font-weight: 400;
  color: #fff;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.reward-box p span {
  font-weight: 700;
  font-style: italic;
}

.reward-box h4 {
  font-weight: 400;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: #fff;
}

.reward-box .btn-one {
  font-weight: 500;
  text-align: center;
  text-transform: capitalize;
  background-color: #f6a525;
  font-family: "Oswald", sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
}

.reward-slogan-image {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
  /* width: 340px; */
}
.reward-logo {
  width: 285px;
  height: 285px;
}
.reward-content {
  margin-left: 10px;
  padding-top: 36px;
  padding-bottom: 174px;
}
.reward-box {
  padding-left: 150px;
  /* border-radius: 9px; */
  /* padding-bottom: 20px; */
  overflow: hidden;
  z-index: 1;
}
.reward-mobile-image {
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  /* opacity: 0.8; */
}

.reward-box h2 {
  font-size: 90px;
}
.reward-box h3 {
  font-size: 44px;
  margin-bottom: 5px;
}
.reward-box p {
  font-size: 24px;
  line-height: 28px;
  width: 530px;
}
.reward-box h4 {
  font-family: "Oswald", sans-serif;
  font-size: 32px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.reward-box .btn-one {
  width: 200px;
  height: 55px;
  border-radius: 9px;
  font-size: 20px;
  letter-spacing: 0.8px;
  color: #282828;
}
.reward-box .btn-two {
  width: 200px;
  height: 55px;
  border-radius: 9px;
  font-size: 20px;
  letter-spacing: 0.8px;
}
.reward-desktop-image {
  position: absolute;
  top: 0;
  right: 0;
  /* width: 612px; */
  z-index: -1;
}

.reward-section {
  padding-top: 110px;
}

.fav-apps-links .img-one {
  position: relative;
  left: 16px;
}

.scroll-button.bg_red {
  color: #fff;
  background-color: #c10a28;
}

.scroll-button {
  position: relative;
  overflow: hidden;
  height: 55px;
  width: 176px;
  font-family: "Oswald", sans-serif;
  font-weight: 500;
  font-size: 20px;
  border: none;
  background-color: #ffc222;
  color: #3e3d42;
  cursor: pointer;
  border-radius: 9px;
  display: inline-block;
}

.scroll-button .text1 {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.1s ease;
}

.scroll-button .scroll-up {
  top: 100%;
}

.scroll-button:hover .text1:first-child {
  transform: translateY(-100%);
}

.scroll-button:hover .scroll-up {
  transform: translateY(-100%);
}

.scroll-button.scroll-button-2 {
  height: 55px;
  width: 200px;
}

.scroll-button.btn-form {
  width: 100%;
  height: 42px;
  background-color: #d32512;
  color: #fff;
  text-transform: capitalize;
  font-size: 16px;
  border-radius: 5px;
}

.scroll-button.scroll-button-3 {
  width: 200px;
  height: 55px;
  letter-spacing: 0.8px;
  background-color: #f6a525;
  color: #282828;
  text-transform: uppercase;
}

.scroll-button.scroll-button-4 {
  color: #ffff;
  border: 1px solid #ffffff;
  box-shadow: 0px 4px 19.5px 0px #0000001a;
  text-transform: uppercase;
  background-color: transparent;
}

.scroll-button.btn-form-footer {
  background-color: #d32512;
  color: #fff;
  letter-spacing: 1px;
  text-align: center;
  width: 99%;
  height: 42px;
  border-radius: 5px;
  font-size: 16px;
  font-family: "Montserrat", sans-serif;
}

.cate-sec-box-2,
.cate-sec-box-1 {
  position: absolute;
  top: 50%;
  width: 100%;
}

.cate-sec-box-2 {
  right: 0;
  transform: translateY(-50%);
}

.cate-sec-box-1 {
  left: 50%;
  transform: translate(-50%, -50%);
}

.product-card-2.card-2.bg-change.bg-change2 {
  background: radial-gradient(50% 50% at 50% 50%, #0002b5 0.48%, #1d1e6a 100%);
}

.faq-title {
  font-weight: 500;
  font-size: 17px;
  text-transform: capitalize;
  font-family: "Oswald", sans-serif;
  color: #212121;
}

.faq-trigger {
  cursor: pointer;
}

.faq-box {
  border-radius: 5px;
  background: #ffffff;
  border-radius: 5px;
  overflow: hidden;
}

.faq-content {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
.faq-content-inner {
  opacity: 0;
  transition: opacity 0.2s ease 0.1s;
}
.faq-content.active .faq-content-inner {
  opacity: 1;
}

.faq-content-title {
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 1px;
  text-transform: capitalize;
  margin-bottom: 8px;
  color: #3c3c3e;
}

.faq-content-desc {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 1px;
  text-transform: capitalize;
  color: #3c3c3e;
}
.collapse {
  display: none;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

.collapse.show {
  display: block;
}
@media (min-width: 1439px) {
  .text-wrapper-categories h3 {
    font-size: 32px;
    line-height: 33.8px;
    letter-spacing: 1px;
  }
  .text-wrapper-categories h2 {
    font-size: 58px;
  }
  .text-wrapper-categories p {
    font-size: 24px;
    line-height: 34px;
  }
  .product-card h3 {
    font-size: 18px;
  }
}

@media (max-width: 1920px) {
  .reward-box {
    padding-left: 30px;
  }

  .reward-desktop-image {
    width: 840px;
  }

  .reward-slogan-image {
    width: 340px;
  }

  .reward-logo {
    width: 230px;
    height: 230px;
  }

  .reward-content {
    padding-bottom: 100px;
  }

  .reward-box h2 {
    font-size: 72px;
  }

  .reward-box h3 {
    font-size: 38px;
    margin-bottom: 5px;
  }

  .reward-box h4 {
    font-size: 28px;
  }

  .reward-box p {
    font-size: 18px;
    line-height: 26px;
    width: 500px;
  }

  .reward-box .container {
    padding: 0;
    /* margin: 0; */
  }
}

@media (max-width: 1399.98px) {
  .pr-text {
    padding-right: 120px;
  }

  .product-card-2.card-1 img,
  .product-card-2.card-2 img {
    width: 254px;
  }
  .text-wrapper-categories h2 {
    font-size: 58px;
  }
  .text-wrapper-categories h3 {
    font-size: 26px;
  }
  .product-card h3 {
    font-size: 17px;
  }
  .product-card a {
    font-size: 18px;
  }
  .reward-box p {
    font-size: 18px;
    line-height: 28px;
    width: 506px;
  }
  .reward-box h3 {
    font-size: 38px;
  }
  .reward-box h4 {
    font-size: 26px;
  }
}

@media (max-width: 1366.98px) {
  .reward-desktop-image {
    width: 730px;
  }

  .reward-box h2 {
    font-size: 60px;
  }

  .reward-box h3 {
    font-size: 32px;
  }

  .reward-content {
    padding-bottom: 60px;
  }

  .reward-slogan-image {
    width: 310px;
  }
  .banner-subtitle {
    font-size: 84px;
  }
}

@media (max-width: 1281.98px) {
  .product-card-2.card-1 p {
    padding-right: 40px;
  }
  .banner-subtitle {
    font-size: 65px;
  }
  .product-card-2.card-2.bg-change h4,
  .product-card-2 h4 {
    font-size: 28px;
  }
  .services-box h3 {
    font-size: 28px;
  }
  .services-box p {
    font-size: 22px;
  }
  /* .product-card-2 h4 {
    font-size: 34px;
  } */
  .product-card-2 h3 {
    font-size: 22px;
  }
  /* .product-card-2.card-2 h4 {
    font-size: 27px;
  } */
  .product-card-2.card-1 img,
  .product-card-2.card-2 img {
    width: 200px;
  }
  .text-wrapper-categories h2 {
    font-size: 52px;
  }
  .text-wrapper-categories p {
    font-size: 20px;
    line-height: 32px;
  }
  .reward-logo {
    width: 240px;
    height: 240px;
  }

  .reward-slogan-image {
    width: 290px;
  }

  .reward-box h2 {
    font-size: 66px;
  }

  .reward-box h3 {
    font-size: 30px;
  }

  .reward-box p {
    width: 400px;
  }

  .reward-desktop-image {
    width: 665px;
  }

  .reward-content {
    padding-bottom: 20px;
  }
  .scroll-button.scroll-button-3 {
    width: 180px;
  }
  .reward-box p {
    width: 380px;
  }
  .reward-slogan-image {
    width: 314px;
  }
}

@media (max-width: 1199.98px) {
  .product-card-2.card-1 img,
  .product-card-2.card-2 img {
    width: auto;
  }
  .services-box {
    height: 248px;
  }
  /* .banner-img-1 {
    width: 820px;
    height: auto;
    object-fit: cover;
  } */
  .reward-slogan-image {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: -1;
    width: 214px;
  }
  .reward-logo {
    width: 185px;
    height: 185px;
  }
  .reward-content {
    margin-left: 10px;
  }
  .reward-box {
    padding-left: 14px;
    /* border-radius: 5px; */
    padding-top: 9px;
    padding-bottom: 8px;
    overflow: hidden;
    z-index: 1;
  }
  .reward-mobile-image {
    position: absolute;
    top: 0;
    right: 0;
    z-index: -1;
    /* opacity: 0.8; */
  }
  .reward-box h2 {
    font-size: 45px;
  }
  .reward-box h3 {
    font-size: 22px;
    margin-bottom: 5px;
  }
  .reward-box p {
    font-size: 14px;
    line-height: 21px;
    width: 300px;
  }
  .reward-box h4 {
    font-family: "Oswald", sans-serif;
    font-size: 20px;
    margin-top: 16px;
    margin-bottom: 14px;
  }
  .reward-box .btn-one {
    width: 170px;
    height: 53px;
    border-radius: 9px;
    font-size: 18px;
    letter-spacing: 0.8px;
    color: #282828;
  }
  .reward-desktop-image {
    position: absolute;
    top: 0;
    right: 0;
    width: 550px;
    z-index: -1;
  }
  .reward-box .btn-two {
    width: 170px;
    height: 53px;
    border-radius: 9px;
    font-size: 18px;
    letter-spacing: 0.8px;
  }
  .reward-content {
    margin-left: 10px;
    padding-top: 36px;
    padding-bottom: 10px;
  }
  .banner-order-now {
    width: 90px;
    height: 90px;
    font-size: 16px;
    right: 130px;
    top: 110px;
  }
  .hero-section {
    height: 1008px;
  }
  .product-card h3 {
    font-size: 13px;
  }
  .product-card a {
    font-size: 15px;
  }

  .text-wrapper-categories h3 {
    font-size: 18px;
  }
  .text-wrapper-categories h2 {
    font-size: 38px;
  }
  .text-wrapper-categories p {
    font-size: 24px;
    line-height: 34px;
  }
  .text-wrapper-categories p {
    font-size: 18px;
    line-height: 28px;
  }
  .scroll-button.scroll-button-3,
  .scroll-button.scroll-button-4 {
    width: 150px;
    font-size: 16px;
  }
  /* .product-card-2 h4 {
    font-size: 18px;
  } */
  .product-card-2 p {
    font-size: 13px;
    line-height: 18px;
  }
  /* .product-card-2.card-1 img {
    top: -55px;
  } */
  /* .product-card-2.card-2 img {
    top: 33px;
  } */

  .banner-title {
    font-size: 44px;
  }
  .banner-subtitle {
    font-size: 72px;
  }

  .services-box p {
    font-size: 14px;
  }
  .services-box h3 {
    font-size: 14px;
  }
}

@media (max-width: 991.98px) {
  .hero-section {
    height: 870px;
  }
  .banner-subtitle {
    font-size: 72px;
  }
  .banner-title {
    font-size: 56px;
  }
  .product-card-2.card-2.bg-change h4,
  .product-card-2 h4 {
    font-size: 24px;
  }
  .reward-content {
    padding-top: 18px;
  }
  .scroll-button.scroll-button-3,
  .scroll-button.scroll-button-4 {
    width: 107px;
    font-size: 12px;
    height: 36px;
    border-radius: 5px;
  }
  .product-card-2.card-1 img,
  .product-card-2.card-2 img {
    width: 200px;
  }
  .product-card-2 {
    height: 310px;
  }
  .scroll-button {
    height: 48px;
    width: 150px;
    font-size: 18px;
  }
  /* .product-card-2 h4 {
    font-size: 30px;
  } */
  .product-card-2 h3 {
    font-size: 20px;
  }
  /* .product-card-2.card-2 h4 {
    font-size: 24px;
  } */
  .footer-top-2 {
    border-top: 1px solid #ffffff1a;
  }
  .footer-top {
    border-bottom: 0;
    padding: 10px 0;
  }
  .product-card-2 p {
    margin: 6px 0;
  }
  .slide .banner-order-now {
    display: none !important;
  }

  .slide.active .banner-order-now {
    display: none !important;
  }
  .reward-desktop-image {
    position: absolute;
    top: 0;
    right: 0;
    width: 440px;
    z-index: -1;
  }
  .services-box {
    height: 155px;
  }
  .reward-slogan-image {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: -1;
    width: 144px;
  }
  .reward-logo {
    width: 120px;
    height: 120px;
  }
  .reward-content {
    margin-left: 0;
  }
  .reward-box {
    padding-left: 14px;
    /* border-radius: 5px; */
    padding-top: 16px;
    padding-bottom: 12px;
    overflow: hidden;
    z-index: 1;
  }
  .reward-mobile-image {
    position: absolute;
    top: 0;
    right: 0;
    z-index: -1;
    max-width: 200px;
    height: auto;
    /* opacity: 0.8; */
  }
  .reward-box h2 {
    font-size: 38px;
    margin-bottom: 4px;
  }
  .reward-box h3 {
    font-size: 18px;
    margin-bottom: 5px;
  }
  .reward-box p {
    font-size: 12px;
    line-height: 17px;
    width: 260px;
  }
  .reward-box h4 {
    font-family: "Oswald", sans-serif;
    font-size: 12px;
    line-height: 15px;
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .reward-box .btn-one {
    width: 76px;
    height: 23px;
    border-radius: 2px;
    font-size: 10px;
    letter-spacing: 0.8px;
    color: #7a2d03;
  }
  .reward-box .btn-two {
    width: 82px;
    height: 23px;
    border-radius: 2px;
    font-size: 10px;
    letter-spacing: 0.8px;
  }

  /* .banner-img-1 {
    width: 308px;
    height: 149px;
  }
  .banner-img-2 {
    width: 179px;
    height: 178px;
  }
  .banner-img-3 {
    width: 238px;
    height: 191px;
  } */
  .banner-order-now {
    display: none;
  }
  .newsletter {
    padding: 14px;
  }
  .scroll-button.scroll-button-2 {
    height: 48px;
    width: 150px;
  }
  .footer-copyright {
    font-size: 14px;
  }

  .footer-address,
  .footer-email {
    font-size: 16px;
    line-height: 27px;
  }
  .footer-desc {
    font-size: 15px;
  }
  .footer-desc2 {
    font-size: 12px;
    line-height: 19px;
  }
  .footer-title {
    font-size: 20px;
  }

  .product-card h3,
  .product-card a {
    font-size: 14px;
  }
  .product-slide {
    padding: 5px;
  }
  .text-wrapper-categories h3 {
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 4px;
    letter-spacing: 0px;
  }

  .text-wrapper-categories h2 {
    font-size: 24px;
    line-height: 100%;
  }

  .text-wrapper-categories p {
    font-size: 14px;
    line-height: 21px;
  }
  /* .banner-logo {
    width: 100px;
    height: 100px;
  }
  .hero-section {
    height: 450px;
  } */

  .categories-section {
    padding-top: 50px;
  }
}

@media (max-width: 767.98px) {
  .product-card-2.card-1 img,
  .product-card-2.card-2 img {
    width: auto;
  }
  .scroll-button.btn-form-footer {
    height: 50px;
  }

  .scroll-button.scroll-button-3 {
    width: 76px;
    height: 23px;
    font-size: 10px;
    border-radius: 2px;
    color: #7a2d03;
    text-transform: capitalize;
  }
  .scroll-button.scroll-button-4 {
    width: 76px;
    height: 23px;
    font-size: 10px;
    border-radius: 2px;
    text-transform: capitalize;
  }
  .scroll-button.btn-form {
    height: 34px;
    font-size: 11px;
  }
  .scroll-button.scroll-button-2 {
    height: 29px;
    width: 156px;
    color: #7a2d03;
  }
  .scroll-button {
    height: 29px;
    width: 74px;
    color: #7a2d03;
    border-radius: 5px;
    font-size: 10px;
  }

  .reward-box p {
    font-size: 10px;
    line-height: 13px;
    width: 200px;
  }
  .reward-box h4 {
    font-family: "Oswald", sans-serif;
    font-size: 11px;
    line-height: 15px;
    margin-top: 8px;
    margin-bottom: 8px;
  }
  .reward-box h2 {
    font-size: 20px;
    margin-bottom: 0px;
  }
  .reward-box h3 {
    font-size: 15px;
    margin-bottom: 5px;
  }
  .fav-apps-section {
    background-color: #f5f5f5;
    margin-top: 60px;
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .reward-section {
    padding-top: 50px;
  }
  .reward-content {
    margin-left: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  .text-wrapper-categories h2 {
    font-size: 18px;
    line-height: 100%;
  }
  .btn-form {
    padding: 8px 0;
    font-size: 11px;
  }
  .newsletter-input {
    height: 34px;
    font-weight: 300;
    font-size: 11px;
  }
  /* .product-card-2.card-2 h4 {
    font-size: 12px;
  } */
  .product-card-2.card-1 p {
    padding-right: 0;
  }
  .product-card-2.card-2.bg-change h4,
  .product-card-2 h4 {
    font-size: 14px;
  }
  .services-box p {
    margin: 16px 0 4px 0;
  }

  .services-section {
    padding-top: 40px;
  }
  .customer-fav-section {
    padding-top: 50px;
  }
  .banner-title,
  .banner-subtitle {
    transform: rotate(-4deg);
    padding-top: 9px;
  }
  .banner-title {
    font-size: 20px;
  }
  .banner-subtitle {
    font-size: 32px;
  }
  .services-title {
    font-size: 20px;
  }
  .services-subtitle {
    font-size: 14px;
  }

  .container {
    overflow-x: hidden;
  }

  /* .service-slide {
    flex: 0 0 50%;
    max-width: 50%;
    padding-left: 6px;
    padding-right: 6px;
  } */

  /* .product-card .img-box img {
    width: 119px;
    height: 66px;
  } */

  /* .product-card .img-box {
    padding: 30px 0;
  } */
  .product-card-2 p {
    font-size: 10px;
    line-height: 12px;
  }
  /* .product-card-2 h4 {
    font-size: 14px;
  } */
  .product-card-2 h3 {
    font-size: 11px;
    margin-bottom: 3px;
  }
  .product-card-2 .content {
    padding: 20px 8px 0;
  }

  /* .product-card-2 img {
    position: relative;
    top: -23px;
  } */
  /* .product-card-2.card-1 img {
    top: 0;
  } */
  .envelope-container {
    width: 172px;
    height: 100%;
  }
  .envelope {
    height: 100%;
  }
  .envelope-img {
    width: 104px;
    top: 32%;
    left: 20%;
  }
  .newsletter {
    padding: 10px;
  }
  .fav-apps-box h3 {
    font-size: 20px;
  }

  .fav-apps-links {
    /* width: 300px; */
    padding: 5px 10px;
  }

  .map-section h3 {
    font-size: 17px;
  }

  .map-section p {
    font-size: 14px;
    line-height: 18px;
  }
  .product-card-2 {
    height: 174px;
  }
  .form-title {
    font-size: 16px;
  }
  .form-desc {
    font-size: 12px;
  }

  .middle-banner-section {
    padding-top: 50px;
  }

  .pr-text {
    padding-right: 0;
  }
  .hero-section {
    height: 596px;
  }
}

@media (max-width: 440px) {
  .product-card-2.card-1 img,
  .product-card-2.card-2 img {
    width: 90px;
  }
  .text-wrapper-categories p {
    font-size: 9px;
    line-height: 12px;
  }
  .product-card h3,
  .product-card a {
    font-size: 11px;
  }
  .product-card h3,
  .product-card a {
    font-size: 11px;
  }
}

@media (max-width: 425px) {
  .reward-box h2 {
    font-size: 18px;
    margin-bottom: 0px;
  }
  .reward-box h3 {
    font-size: 11px;
    margin-bottom: 2px;
  }
  .reward-box p {
    font-size: 10px;
    line-height: 13px;
    width: 200px;
  }
  .reward-box h4 {
    font-size: 9px;
    line-height: 15px;
    margin-top: 1px;
    margin-bottom: 3px;
  }
  .reward-mobile-image {
    max-width: 185px;
  }
  .reward-box {
    padding-left: 24px;
    border-radius: 5px;
    padding-top: 16px;
    padding-bottom: 27px;
  }
}

@media (max-width: 375px) {
  .product-card-2 h3 {
    font-size: 10px;
  }
  .product-card-2.card-2.bg-change h4,
  .product-card-2 h4 {
    font-size: 12px;
  }
  .reward-box {
    padding-left: 10px;
    padding-bottom: 18px;
  }
  .reward-mobile-image {
    max-width: 172px;
  }
  .reward-box p {
    font-size: 8px;
    line-height: 12px;
    width: 172px;
  }
}

@media (max-width: 354px) {
  .reward-mobile-image {
    max-width: 159px;
    height: 100%;
  }
  .product-card-2 h3 {
    font-size: 9px;
  }
  /* .product-card-2.card-2 h4 {
    font-size: 11px;
  } */
  .text-wrapper-categories h3 {
    font-size: 9px;
    line-height: 16px;
    margin-bottom: 0px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}
@media (min-width: 1366px) {
  .container {
    max-width: 1366px;
  }
}
@media (min-width: 1440px) {
  .container {
    max-width: 1440px;
  }
}
@media (min-width: 1920px) {
  .reward-desktop-image {
    width: 840px;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}
