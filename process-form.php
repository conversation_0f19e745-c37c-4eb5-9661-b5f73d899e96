<?php
// Turn off error display for production
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Log errors instead of displaying them
ini_set('log_errors', 1);
ini_set('error_log', 'form-errors.log');

// Include PHPMailer classes
use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Check if PHPMailer is installed
if (file_exists('vendor/autoload.php')) {
    require 'vendor/autoload.php';
} else {
    if (file_exists('PHPMailer/src/Exception.php')) {
        require 'PHPMailer/src/Exception.php';
        require 'PHPMailer/src/PHPMailer.php';
        require 'PHPMailer/src/SMTP.php';
    } else {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'PHPMailer not found. Please install PHPMailer.']);
        exit;
    }
}

// Gmail SMTP Configuration
$smtp_host = 'smtp.gmail.com';
$smtp_username = '<EMAIL>'; // Gmail address
$smtp_password = 'zsxn zpcg psmw iyav'; // App password
$smtp_port = 587; // TLS port
$smtp_secure = 'tls';
$client_email = '<EMAIL>'; // Receiver email (same as sender)

// Function to send email
function sendEmail($subject, $body, $from_name, $from_email) {
    global $smtp_host, $smtp_username, $smtp_password, $smtp_port, $smtp_secure, $client_email;

    $mail = new PHPMailer(true);

    try {
        $mail->isSMTP();
        $mail->Host = $smtp_host;
        $mail->SMTPAuth = true;
        $mail->Username = $smtp_username;
        $mail->Password = $smtp_password;
        $mail->SMTPSecure = $smtp_secure;
        $mail->Port = $smtp_port;
        $mail->SMTPDebug = 0;

        $mail->setFrom($smtp_username, 'Bruno\'s Pizza Website');
        $mail->addAddress($client_email);
        $mail->addReplyTo($from_email, $from_name);

        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("Mailer Error: " . $mail->ErrorInfo);
        return false;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'An error occurred'];

    if (isset($_POST['form_type'])) {
        $form_type = $_POST['form_type'];

        if ($form_type === 'newsletter') {
            $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
            $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);

            if (!empty($name) && !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $subject = 'New Newsletter Subscription';
                $body = "
                    <h2>New Newsletter Subscription</h2>
                    <p><strong>Name:</strong> {$name}</p>
                    <p><strong>Email:</strong> {$email}</p>
                ";

                if (sendEmail($subject, $body, $name, $email)) {
                    $response = ['success' => true, 'message' => 'Thank you for subscribing to our newsletter!'];
                } else {
                    $response = ['success' => false, 'message' => 'Failed to send email. Please try again later.'];
                }
            } else {
                $response = ['success' => false, 'message' => 'Please fill in all required fields with valid information.'];
            }
        } else if ($form_type === 'contact') {
            $first_name = filter_input(INPUT_POST, 'first_name', FILTER_SANITIZE_STRING);
            $last_name = filter_input(INPUT_POST, 'last_name', FILTER_SANITIZE_STRING);
            $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
            $message = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_STRING);

            if (!empty($first_name) && !empty($last_name) && !empty($email) && !empty($message) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $subject = 'New Contact Form Submission';
                $body = "
                    <h2>New Contact Form Submission</h2>
                    <p><strong>Name:</strong> {$first_name} {$last_name}</p>
                    <p><strong>Email:</strong> {$email}</p>
                    <p><strong>Message:</strong></p>
                    <p>{$message}</p>
                ";

                if (sendEmail($subject, $body, "$first_name $last_name", $email)) {
                    $response = ['success' => true, 'message' => 'Thank you for your message! We will get back to you soon.'];
                } else {
                    $response = ['success' => false, 'message' => 'Failed to send email. Please try again later.'];
                }
            } else {
                $response = ['success' => false, 'message' => 'Please fill in all required fields with valid information.'];
            }
        }
    }

    echo json_encode($response);
    exit;
}
?>
